import {
	IExecuteFunctions,
	INodeExecutionData,
	INodeType,
	INodeTypeDescription,
	NodeConnectionTypes,
} from 'n8n-workflow';

export class ParallelWorkflowExecutor implements INodeType {
	description: INodeTypeDescription = {
		displayName: 'Parallel Workflow Executor',
		name: 'parallelWorkflowExecutor',
		icon: 'fa:tasks',
		group: ['transform'],
		version: 1,
		subtitle: '={{"Workflow: " + $parameter["workflowId"] + " | Concurrency: " + $parameter["concurrency"]}}',
		description: 'Execute a workflow multiple times in parallel with controlled concurrency',
		defaults: {
			name: 'Parallel Workflow Executor',
			color: '#4A90E2',
		},
		inputs: [NodeConnectionTypes.Main],
		outputs: [NodeConnectionTypes.Main],
		properties: [
			{
				displayName: 'Workflow',
				name: 'workflowId',
				type: 'workflowSelector',
				default: '',
				required: true,
				description: 'The workflow to execute in parallel',
			},
			{
				displayName: 'Data Mode',
				name: 'dataMode',
				type: 'options',
				options: [
					{
						name: 'Each Input Item Separately',
						value: 'each',
						description: 'Execute the workflow once for each input item',
					},
					{
						name: 'All Input Items at Once',
						value: 'once',
						description: 'Execute the workflow once with all input items',
					},
				],
				default: 'each',
				description: 'How to pass the input data to the workflow',
			},
			{
				displayName: 'Concurrency',
				name: 'concurrency',
				type: 'number',
				default: 5,
				typeOptions: {
					minValue: 1,
					maxValue: 50,
				},
				description: 'Maximum number of parallel executions',
			},
			{
				displayName: 'On Error',
				name: 'onError',
				type: 'options',
				options: [
					{
						name: 'Continue Execution',
						value: 'continue',
						description: 'Continue executing other workflows even if some fail',
					},
					{
						name: 'Stop All Executions',
						value: 'stopAll',
						description: 'Stop all executions if any workflow fails',
					},
				],
				default: 'continue',
				description: 'What to do when a workflow execution fails',
			},
			{
				displayName: 'Wait for Sub-Workflow',
				name: 'waitForSubWorkflow',
				type: 'boolean',
				default: true,
				description: 'Whether to wait for the sub-workflow to finish execution before continuing',
			},
			{
				displayName: 'Preserve Order',
				name: 'preserveOrder',
				type: 'boolean',
				default: true,
				description: 'Whether to preserve the original order of results',
			},
			{
				displayName: 'Include Execution Metadata',
				name: 'includeMetadata',
				type: 'boolean',
				default: false,
				description: 'Whether to include execution metadata in the results',
			},
		],
	};

	async execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {
		const items = this.getInputData();
		const workflowId = this.getNodeParameter('workflowId', 0) as { value: string };
		const dataMode = this.getNodeParameter('dataMode', 0) as string;
		const concurrency = this.getNodeParameter('concurrency', 0) as number;
		const onError = this.getNodeParameter('onError', 0) as string;
		const options = this.getNodeParameter('options', 0, {}) as {
			waitForSubWorkflow?: boolean;
			preserveOrder?: boolean;
			includeMetadata?: boolean;
		};

		const waitForSubWorkflow = options.waitForSubWorkflow ?? true;
		const preserveOrder = options.preserveOrder ?? true;
		const includeMetadata = options.includeMetadata ?? false;

		if (dataMode === 'each') {
			// Execute workflow for each input item with concurrency control
			const results: (INodeExecutionData[] | null)[] = new Array(items.length).fill(null);
			const errors: (Error | null)[] = new Array(items.length).fill(null);
			const pool: Promise<void>[] = [];
			let currentIndex = 0;
			let hasError = false;

			const executeItem = async (itemIndex: number): Promise<void> => {
				try {
					if (waitForSubWorkflow) {
						const executionResult = await this.executeWorkflow(
							{ id: workflowId.value },
							[items[itemIndex]],
						);

						if (executionResult.data?.main) {
							results[itemIndex] = executionResult.data.main[0] || [];

							if (includeMetadata && results[itemIndex]) {
								results[itemIndex]!.forEach((item) => {
									if (!item.metadata) item.metadata = {};
									item.metadata.subExecution = {
										workflowId: workflowId.value,
										executionId: executionResult.executionId,
									};
								});
							}
						} else {
							results[itemIndex] = [];
						}
					} else {
						const executionResult = await this.executeWorkflow(
							{ id: workflowId.value },
							[items[itemIndex]],
						);

						results[itemIndex] = [
							{
								...items[itemIndex],
								metadata: includeMetadata ? {
									subExecution: {
										workflowId: workflowId.value,
										executionId: executionResult.executionId,
									},
								} : undefined,
							},
						];
					}
				} catch (error) {
					errors[itemIndex] = error instanceof Error ? error : new Error(String(error));

					if (onError === 'stopAll') {
						hasError = true;
					} else if (this.continueOnFail()) {
						results[itemIndex] = [
							{
								json: { error: error instanceof Error ? error.message : String(error) },
								pairedItem: { item: itemIndex },
							},
						];
					} else {
						hasError = true;
					}
				}
			};

			// Process items with concurrency control using Promise pool
			while (currentIndex < items.length || pool.length > 0) {
				// Fill the pool up to concurrency limit
				while (pool.length < concurrency && currentIndex < items.length && !hasError) {
					const itemIndex = currentIndex++;
					const promise = executeItem(itemIndex);
					pool.push(promise);
				}

				if (pool.length === 0) break;

				// Wait for at least one promise to complete
				await Promise.race(pool);

				// Remove completed promises from pool
				for (let i = pool.length - 1; i >= 0; i--) {
					const promise = pool[i];
					// Check if promise is resolved by racing it with a resolved promise
					const isResolved = await Promise.race([
						promise.then(() => true, () => true),
						Promise.resolve(false)
					]);

					if (isResolved) {
						pool.splice(i, 1);
					}
				}

				// Check if we should stop on error
				if (hasError && onError === 'stopAll') {
					break;
				}
			}

			// Handle any remaining errors
			for (let i = 0; i < errors.length; i++) {
				if (errors[i] && !this.continueOnFail() && onError === 'stopAll') {
					throw errors[i];
				}
			}

			// Prepare final results
			if (preserveOrder) {
				const finalResults: INodeExecutionData[][] = [];
				for (let i = 0; i < results.length; i++) {
					if (results[i] !== null) {
						finalResults.push(results[i]!);
					}
				}
				return finalResults;
			} else {
				// Return results flattened
				const allResults: INodeExecutionData[] = [];
				for (const result of results) {
					if (result !== null) {
						allResults.push(...result);
					}
				}
				return [allResults];
			}
		} else {
			// Execute workflow once with all items
			try {
				const executionResult = await this.executeWorkflow(
					{ id: workflowId.value },
					items,
				);

				if (!waitForSubWorkflow) {
					return [items];
				}

				if (!executionResult.data?.main) {
					return [[]];
				}

				return executionResult.data.main;
			} catch (error) {
				if (this.continueOnFail()) {
					return [
						[
							{
								json: { error: error instanceof Error ? error.message : String(error) },
								pairedItem: { item: 0 },
							},
						],
					];
				}
				throw error;
			}
		}
	}
}
