# n8n-nodes-parallel-workflow-executor

这是一个n8n自定义节点，用于并行执行工作流，支持并发控制。

## 功能特性

- 🚀 **并行执行**: 支持同时执行多个工作流实例
- ⚡ **并发控制**: 可配置最大并发数量（1-50）
- 🔄 **数据模式**: 支持每个输入项单独执行或所有数据一次传递
- 🛡️ **错误处理**: 可选择继续执行或在错误时停止所有执行
- 📊 **结果管理**: 支持保持原始顺序和包含执行元数据
- ⏱️ **异步控制**: 可选择等待子工作流完成或异步执行

## 安装

```bash
npm install n8n-nodes-parallel-workflow-executor
```

## 配置参数

### 基础配置

- **Workflow**: 选择要并行执行的目标工作流
- **Data Mode**: 
  - `Each Input Item Separately`: 为每个输入项单独执行工作流
  - `All Input Items at Once`: 将所有输入项一次性传递给工作流
- **Concurrency**: 最大并发执行数量（1-50，默认5）
- **On Error**: 错误处理策略
  - `Continue Execution`: 即使某些执行失败也继续其他执行
  - `Stop All Executions`: 任何执行失败时停止所有执行

### 高级选项

- **Wait for Sub-Workflow**: 是否等待子工作流完成（默认true）
- **Preserve Order**: 是否保持结果的原始顺序（默认true）
- **Include Execution Metadata**: 是否在结果中包含执行元数据（默认false）

## 使用场景

1. **批量数据处理**: 对大量数据项并行执行相同的处理流程
2. **API调用优化**: 并行调用多个API以提高效率
3. **数据同步**: 同时向多个目标系统同步数据
4. **负载分散**: 将工作负载分散到多个并行执行中

## 示例

### 场景1: 并行处理用户数据
```
输入: 100个用户记录
配置: Concurrency = 10, Data Mode = "Each Input Item Separately"
结果: 同时处理10个用户，总共需要10轮并行执行
```

### 场景2: 批量API调用
```
输入: 50个API请求参数
配置: Concurrency = 5, On Error = "Continue Execution"
结果: 最多同时进行5个API调用，失败的请求不影响其他请求
```

## 技术实现

- 使用Promise池模式控制并发数量
- 支持错误处理和结果收集
- 保持结果的原始顺序
- 兼容n8n工作流执行机制

## 注意事项

- 高并发可能会对目标系统造成压力，请合理设置并发数量
- 确保目标工作流能够正确处理传入的数据格式
- 在生产环境中建议先进行小规模测试

## 许可证

MIT

## 贡献

欢迎提交Issue和Pull Request来改进这个节点。
